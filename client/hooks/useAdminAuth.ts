"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"

interface AdminUser {
  id: string
  name: string
  cpf: string
  role: string
  isAdmin: boolean
  createdAt: string
}

interface AdminAuthResponse {
  isAdmin: boolean
  user: AdminUser
  permissions: string[]
}

interface AdminAuthState {
  isAuthenticated: boolean
  isAdmin: boolean
  user: AdminUser | null
  permissions: string[]
  loading: boolean
  error: string | null
}

export function useAdminAuth() {
  const router = useRouter()
  const [state, setState] = useState<AdminAuthState>({
    isAuthenticated: false,
    isAdmin: false,
    user: null,
    permissions: [],
    loading: true,
    error: null,
  })

  const verifyAdminAuth = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      // Check if we have a token
      const savedState = localStorage.getItem("x-auth-state")
      if (!savedState) {
        throw new Error("No authentication token found")
      }

      const parsed = JSON.parse(savedState)
      if (!parsed.authToken) {
        throw new Error("Invalid authentication state")
      }

      // Verify admin status with backend
      const response = await fetch("/api/admin/auth/verify", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${parsed.authToken}`,
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Token expired or invalid")
        } else if (response.status === 403) {
          throw new Error("Access denied - admin privileges required")
        } else {
          throw new Error("Failed to verify admin authentication")
        }
      }

      const adminData: AdminAuthResponse = await response.json()

      setState({
        isAuthenticated: true,
        isAdmin: adminData.isAdmin,
        user: adminData.user,
        permissions: adminData.permissions,
        loading: false,
        error: null,
      })

      return true
    } catch (error) {
      console.error("Admin auth verification failed:", error)
      
      // Clear invalid auth state
      localStorage.removeItem("x-auth-state")
      
      setState({
        isAuthenticated: false,
        isAdmin: false,
        user: null,
        permissions: [],
        loading: false,
        error: error instanceof Error ? error.message : "Authentication failed",
      })

      return false
    }
  }, [])

  const logout = useCallback(() => {
    localStorage.removeItem("x-auth-state")
    setState({
      isAuthenticated: false,
      isAdmin: false,
      user: null,
      permissions: [],
      loading: false,
      error: null,
    })
    router.push("/")
  }, [router])

  const redirectToLogin = useCallback(() => {
    router.push("/")
  }, [router])

  const hasPermission = useCallback((permission: string) => {
    return state.permissions.includes(permission)
  }, [state.permissions])

  // Auto-verify on mount
  useEffect(() => {
    verifyAdminAuth()
  }, [verifyAdminAuth])

  return {
    ...state,
    verifyAdminAuth,
    logout,
    redirectToLogin,
    hasPermission,
  }
}
