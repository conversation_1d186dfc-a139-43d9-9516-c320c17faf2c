"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  DollarSign,
  Building,
  User,
  RefreshCw,
  AlertCircle,
  ExternalLink,
} from "lucide-react"

interface PendingRequest {
  id: string
  rentAmount: number
  monthsToAdvance: number
  proposalAmount?: number
  monthlyRentOffer?: number
  proposedMonths?: number
  contractPdfUrl?: string
  identityDocUrl?: string
  currentStatus: string
  createdAt: string
  updatedAt: string
  user: {
    name: string
    cpf: string
    phone?: string
  }
  realEstate?: {
    name: string
    cnpj: string
  }
  contractData?: {
    propertyAddress?: string
    landlordName?: string
    tenantName?: string
  }
}

interface PendingResponse {
  operations: PendingRequest[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function PendingApprovalsPage() {
  const [requests, setRequests] = useState<PendingResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedRequest, setSelectedRequest] = useState<PendingRequest | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [reason, setReason] = useState("")

  const fetchPendingRequests = async (page: number = 1) => {
    setLoading(true)
    setError("")

    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (!savedState) {
        throw new Error("No authentication found")
      }

      const parsed = JSON.parse(savedState)
      const response = await fetch(`/api/rental-advance/admin/pending-reviews?page=${page}&limit=10`, {
        headers: {
          "Authorization": `Bearer ${parsed.authToken}`,
        },
      })

      if (!response.ok) {
        throw new Error("Failed to fetch pending requests")
      }

      const data = await response.json()
      setRequests(data)
      setCurrentPage(page)
    } catch (error) {
      console.error("Error fetching pending requests:", error)
      setError("Erro ao carregar solicitações pendentes")
    } finally {
      setLoading(false)
    }
  }

  const handleDecision = async (operationId: string, decision: "approved" | "rejected") => {
    setActionLoading(operationId)

    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (!savedState) {
        throw new Error("No authentication found")
      }

      const parsed = JSON.parse(savedState)
      const response = await fetch("/api/rental-advance/admin/review-decision", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${parsed.authToken}`,
        },
        body: JSON.stringify({
          operationId,
          decision,
          reason: reason.trim() || undefined,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to process decision")
      }

      // Refresh the list
      await fetchPendingRequests(currentPage)
      setSelectedRequest(null)
      setReason("")
    } catch (error) {
      console.error("Error processing decision:", error)
      setError("Erro ao processar decisão")
    } finally {
      setActionLoading(null)
    }
  }

  useEffect(() => {
    fetchPendingRequests()
  }, [])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  if (loading && !requests) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Aprovações Pendentes</h2>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            Carregando...
          </Button>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Aprovações Pendentes</h2>
          <p className="text-gray-600">
            {requests?.pagination.total || 0} solicitação{requests?.pagination.total !== 1 ? "ões" : ""} aguardando revisão
          </p>
        </div>
        <Button onClick={() => fetchPendingRequests(currentPage)}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Requests List */}
      {requests && (
        <>
          {requests.operations.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma aprovação pendente</h3>
                <p className="text-gray-600">Todas as solicitações foram processadas!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {requests.operations.map((request) => (
                <Card key={request.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{request.user.name}</h3>
                          <Badge variant="outline">ID: {request.id.slice(-8)}</Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">CPF:</span>
                            <span className="font-medium">{formatCPF(request.user.cpf)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Aluguel:</span>
                            <span className="font-medium">{formatCurrency(request.rentAmount)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Meses:</span>
                            <span className="font-medium">{request.monthsToAdvance}</span>
                          </div>
                          
                          {request.realEstate && (
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Imobiliária:</span>
                              <span className="font-medium">{request.realEstate.name}</span>
                            </div>
                          )}
                          
                          {request.proposalAmount && (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span className="text-gray-600">Proposta:</span>
                              <span className="font-medium text-green-600">{formatCurrency(request.proposalAmount)}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Criado:</span>
                            <span className="font-medium">{formatDate(request.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedRequest(request)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Detalhes
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Detalhes da Solicitação</DialogTitle>
                            </DialogHeader>
                            {selectedRequest && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <h4 className="font-semibold mb-2">Informações do Usuário</h4>
                                    <p><strong>Nome:</strong> {selectedRequest.user.name}</p>
                                    <p><strong>CPF:</strong> {formatCPF(selectedRequest.user.cpf)}</p>
                                    <p><strong>Telefone:</strong> {selectedRequest.user.phone || "N/A"}</p>
                                  </div>
                                  <div>
                                    <h4 className="font-semibold mb-2">Dados da Solicitação</h4>
                                    <p><strong>Valor do Aluguel:</strong> {formatCurrency(selectedRequest.rentAmount)}</p>
                                    <p><strong>Meses:</strong> {selectedRequest.monthsToAdvance}</p>
                                    {selectedRequest.proposalAmount && (
                                      <p><strong>Valor da Proposta:</strong> {formatCurrency(selectedRequest.proposalAmount)}</p>
                                    )}
                                  </div>
                                </div>
                                
                                {selectedRequest.contractData && (
                                  <div>
                                    <h4 className="font-semibold mb-2">Dados do Contrato</h4>
                                    <p><strong>Endereço:</strong> {selectedRequest.contractData.propertyAddress || "N/A"}</p>
                                    <p><strong>Locador:</strong> {selectedRequest.contractData.landlordName || "N/A"}</p>
                                    <p><strong>Locatário:</strong> {selectedRequest.contractData.tenantName || "N/A"}</p>
                                  </div>
                                )}
                                
                                <div>
                                  <h4 className="font-semibold mb-2">Documentos</h4>
                                  <div className="flex gap-2">
                                    {selectedRequest.contractPdfUrl && (
                                      <Button variant="outline" size="sm" asChild>
                                        <a href={selectedRequest.contractPdfUrl} target="_blank" rel="noopener noreferrer">
                                          <ExternalLink className="h-4 w-4 mr-2" />
                                          Contrato PDF
                                        </a>
                                      </Button>
                                    )}
                                    {selectedRequest.identityDocUrl && (
                                      <Button variant="outline" size="sm" asChild>
                                        <a href={selectedRequest.identityDocUrl} target="_blank" rel="noopener noreferrer">
                                          <ExternalLink className="h-4 w-4 mr-2" />
                                          Documento de Identidade
                                        </a>
                                      </Button>
                                    )}
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="font-semibold mb-2">Motivo da Decisão (Opcional)</h4>
                                  <Textarea
                                    placeholder="Digite o motivo da aprovação ou rejeição..."
                                    value={reason}
                                    onChange={(e) => setReason(e.target.value)}
                                    rows={3}
                                  />
                                </div>
                                
                                <div className="flex gap-2 pt-4">
                                  <Button
                                    onClick={() => handleDecision(selectedRequest.id, "approved")}
                                    disabled={actionLoading === selectedRequest.id}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    {actionLoading === selectedRequest.id ? (
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <CheckCircle className="h-4 w-4 mr-2" />
                                    )}
                                    Aprovar
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    onClick={() => handleDecision(selectedRequest.id, "rejected")}
                                    disabled={actionLoading === selectedRequest.id}
                                  >
                                    {actionLoading === selectedRequest.id ? (
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <XCircle className="h-4 w-4 mr-2" />
                                    )}
                                    Rejeitar
                                  </Button>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Pagination */}
              {requests.pagination.totalPages > 1 && (
                <div className="flex items-center justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchPendingRequests(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                  >
                    Anterior
                  </Button>
                  
                  <span className="text-sm text-gray-600">
                    Página {currentPage} de {requests.pagination.totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchPendingRequests(currentPage + 1)}
                    disabled={currentPage === requests.pagination.totalPages || loading}
                  >
                    Próxima
                  </Button>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  )
}
