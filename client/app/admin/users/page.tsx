"use client"

import Re<PERSON>, { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  User,
  Phone,
  Calendar,
  FileText,
  Eye,
  RefreshCw,
  AlertCircle,
} from "lucide-react"

interface UserSearchResult {
  id: string
  name: string
  cpf: string
  phone?: string
  role: string
  isAdmin: boolean
  createdAt: string
  requestCount: number
}

interface SearchResponse {
  users: UserSearchResult[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function UserSearchPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchType, setSearchType] = useState<"cpf" | "name">("cpf")
  const [results, setResults] = useState<SearchResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [currentPage, setCurrentPage] = useState(1)

  const searchUsers = async (page: number = 1) => {
    if (!searchTerm.trim()) {
      setError("Digite um CPF ou nome para buscar")
      return
    }

    setLoading(true)
    setError("")

    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (!savedState) {
        throw new Error("No authentication found")
      }

      const parsed = JSON.parse(savedState)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
      })

      if (searchType === "cpf") {
        params.append("cpf", searchTerm.replace(/\D/g, ""))
      } else {
        params.append("name", searchTerm)
      }

      const response = await fetch(`/api/admin/users/search?${params}`, {
        headers: {
          "Authorization": `Bearer ${parsed.authToken}`,
        },
      })

      if (!response.ok) {
        throw new Error("Failed to search users")
      }

      const data = await response.json()
      setResults(data)
      setCurrentPage(page)
    } catch (error) {
      console.error("Error searching users:", error)
      setError("Erro ao buscar usuários")
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    searchUsers(1)
  }

  const handlePageChange = (page: number) => {
    searchUsers(page)
  }

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const formatPhone = (phone?: string) => {
    if (!phone) return "N/A"
    return phone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Buscar Usuários</h2>
        <p className="text-gray-600">Encontre usuários por CPF ou nome e visualize suas solicitações</p>
      </div>

      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle>Buscar Usuário</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex gap-2">
              <Button
                variant={searchType === "cpf" ? "default" : "outline"}
                onClick={() => setSearchType("cpf")}
                size="sm"
              >
                CPF
              </Button>
              <Button
                variant={searchType === "name" ? "default" : "outline"}
                onClick={() => setSearchType("name")}
                size="sm"
              >
                Nome
              </Button>
            </div>
            <div className="flex-1 flex gap-2">
              <Input
                placeholder={searchType === "cpf" ? "Digite o CPF (apenas números)" : "Digite o nome"}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                className="flex-1"
              />
              <Button onClick={handleSearch} disabled={loading}>
                {loading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
                Buscar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle>
              Resultados da Busca ({results.pagination.total} usuário{results.pagination.total !== 1 ? "s" : ""} encontrado{results.pagination.total !== 1 ? "s" : ""})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {results.users.length === 0 ? (
              <div className="text-center py-8">
                <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Nenhum usuário encontrado</p>
              </div>
            ) : (
              <div className="space-y-4">
                {results.users.map((user) => (
                  <div key={user.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
                          {user.isAdmin && (
                            <Badge variant="secondary">Admin</Badge>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">CPF:</span>
                            <span className="font-medium">{formatCPF(user.cpf)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Telefone:</span>
                            <span className="font-medium">{formatPhone(user.phone)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Cadastro:</span>
                            <span className="font-medium">{formatDate(user.createdAt)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Solicitações:</span>
                            <span className="font-medium">{user.requestCount}</span>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/admin/users/${user.id}`, "_blank")}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Ver Detalhes
                      </Button>
                    </div>
                  </div>
                ))}

                {/* Pagination */}
                {results.pagination.totalPages > 1 && (
                  <div className="flex items-center justify-center gap-2 pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || loading}
                    >
                      Anterior
                    </Button>
                    
                    <span className="text-sm text-gray-600">
                      Página {currentPage} de {results.pagination.totalPages}
                    </span>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === results.pagination.totalPages || loading}
                    >
                      Próxima
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
