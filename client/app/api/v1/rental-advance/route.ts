import { NextRequest, NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:3000"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '10'
    const status = searchParams.get('status')
    
    // Get authorization header from the request
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader) {
      return NextResponse.json(
        {
          success: false,
          message: "Token de autorização é obrigatório",
          error: "UNAUTHORIZED",
        },
        { status: 401 }
      )
    }

    // Build query parameters
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...(status && { status }),
    })

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/rental-advance?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": authHeader,
      },
    })

    const data = await response.json()

    // Return the response with the same status code
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error("Error in rental-advance API route:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Erro interno do servidor",
        error: "INTERNAL_SERVER_ERROR",
      },
      { status: 500 }
    )
  }
}
