"use client"

import type React from "react"
import { useEffect, use<PERSON><PERSON>back, useReducer } from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import {
  CreditCard,
  Smartphone,
  Smile,
  Clock,
  ArrowRight,
  CheckCircle,

  Phone,
  TrendingUp,
  Verified,
  BadgeCheck,
  ShieldCheck,
  Zap,
  Lock,
  Star,
  Check,
} from "lucide-react"
import Image from "next/image"

// Validation schemas
const cpfSchema = z.object({
  cpf: z.string().min(14, "CPF deve ter 11 dígitos"),
})

const registrationSchema = z.object({
  nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  whatsapp: z.string().min(14, "WhatsApp deve ter pelo menos 10 dígitos"),
})

const verificationSchema = z.object({
  code: z.string().length(6, "Código deve ter 6 dígitos"),
})

// Utility functions
const maskCPF = (value: string) => {
  return value
    .replace(/\D/g, "")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d{1,2})/, "$1-$2")
    .replace(/(-\d{2})\d+?$/, "$1")
}

const maskPhone = (value: string) => {
  return value
    .replace(/\D/g, "")
    .replace(/(\d{2})(\d)/, "($1) $2")
    .replace(/(\d{4})(\d)/, "$1-$2")
    .replace(/(\d{4})-(\d)(\d{4})/, "$1$2-$3")
    .replace(/(-\d{4})\d+?$/, "$1")
}

export default function LocPayPage() {
  // --- Types and Interfaces ---
  interface AuthState {
    loading: boolean
    loadingMsg: string
    error: string
    success: string
    authStep: "cpf" | "registration" | "verification" | "complete"
    isNewUser: boolean
    verificationCode: string
    authToken: string | null
    user: any | null
    formData: {
      cpf: string
      nomeCompleto: string
      whatsapp: string
    }
  }

  type Action =
    | { type: "SET_LOADING"; payload: { loading: boolean; msg: string } }
    | { type: "SET_FORM_DATA"; payload: Partial<AuthState["formData"]> }
    | { type: "SET_ERROR"; payload: string }
    | { type: "SET_SUCCESS"; payload: string }
    | { type: "SET_AUTH_STEP"; payload: "cpf" | "registration" | "verification" | "complete" }
    | { type: "SET_IS_NEW_USER"; payload: boolean }
    | { type: "SET_VERIFICATION_CODE"; payload: string }
    | { type: "SET_AUTH_TOKEN"; payload: string | null }
    | { type: "SET_USER"; payload: any | null }
    | { type: "RESET_STATE" }

  // --- Initial State ---
  const INITIAL_STATE: AuthState = {
    loading: false,
    loadingMsg: "",
    error: "",
    success: "",
    authStep: "cpf",
    isNewUser: false,
    verificationCode: "",
    authToken: null,
    user: null,
    formData: {
      cpf: "",
      nomeCompleto: "",
      whatsapp: "",
    },
  }



  // --- Zod Schemas ---
  const cpfSchema = z.object({
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf) => validateCPF(cpf), "CPF inválido"),
  })

  const registrationSchema = z.object({
    nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf: string) => validateCPF(cpf), "CPF inválido"),
    whatsapp: z.string().min(10, "WhatsApp deve ter pelo menos 10 dígitos"),
  })

  const verificationSchema = z.object({
    code: z.string().length(6, "Código deve ter 6 dígitos"),
  })

  const firstPageSchema = z.object({
    nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf: string) => validateCPF(cpf), "CPF inválido"),
    whatsapp: z.string().min(10, "WhatsApp deve ter pelo menos 10 dígitos"),
    jaEhCliente: z.boolean(),
  })

  const secondPageSchema = z.object({
    valorAluguelLiquido: z.string().min(1, "Valor do aluguel é obrigatório"),
    mesesAntecipacao: z.string().min(1, "Número de meses é obrigatório"),
    imobiliaria: z.string().min(1, "Selecione uma imobiliária"),
    contratoFileName: z.string().min(1, "Upload do contrato é obrigatório"),
    dataConsent: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
  })

  const pixFormSchema = z.object({
    pixKey: z.string().min(1, "Chave PIX é obrigatória"),
    documentFile: z.string().min(1, "Documento com foto é obrigatório"),
    termsAccepted: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
  })

  // --- Reducer ---
  const reducer = (state: AuthState, action: Action): AuthState => {
    switch (action.type) {
      case "SET_LOADING":
        return { ...state, loading: action.payload.loading, loadingMsg: action.payload.msg, ...(action.payload.loading ? { error: "", success: "" } : {}) }
      case "SET_FORM_DATA":
        return { ...state, formData: { ...state.formData, ...action.payload }, error: "", success: "" }
      case "SET_ERROR":
        return { ...state, error: action.payload, success: "" }
      case "SET_SUCCESS":
        return { ...state, success: action.payload, error: "" }
      case "SET_AUTH_STEP":
        return { ...state, authStep: action.payload, error: "", success: "" }
      case "SET_IS_NEW_USER":
        return { ...state, isNewUser: action.payload }
      case "SET_VERIFICATION_CODE":
        return { ...state, verificationCode: action.payload }
      case "SET_AUTH_TOKEN":
        return { ...state, authToken: action.payload }
      case "SET_USER":
        return { ...state, user: action.payload }
      case "RESET_STATE":
        return INITIAL_STATE
      default:
        return state
    }
  }

  // --- Main Component State ---
  const [state, dispatch] = useReducer(reducer, INITIAL_STATE)

  // --- Helper Functions ---
  // Função para validar CPF matematicamente
  const validateCPF = useCallback((cpf: string): boolean => {
    const strCPF = cpf.replace(/\D/g, "")

    if (strCPF.length !== 11) return false

    // Verifica se todos os dígitos são iguais
    if ([
      '00000000000',
      '11111111111',
      '22222222222',
      '33333333333',
      '44444444444',
      '55555555555',
      '66666666666',
      '77777777777',
      '88888888888',
      '99999999999',
    ].indexOf(strCPF) !== -1) return false

    // Validação do primeiro dígito verificador
    let soma = 0
    for (let i = 1; i <= 9; i++) {
      soma = soma + parseInt(strCPF.substring(i - 1, i)) * (11 - i)
    }
    let resto = (soma * 10) % 11
    if ((resto === 10) || (resto === 11)) resto = 0
    if (resto !== parseInt(strCPF.substring(9, 10))) return false

    // Validação do segundo dígito verificador
    soma = 0
    for (let i = 1; i <= 10; i++) {
      soma = soma + parseInt(strCPF.substring(i - 1, i)) * (12 - i)
    }
    resto = (soma * 10) % 11
    if ((resto === 10) || (resto === 11)) resto = 0
    if (resto !== parseInt(strCPF.substring(10, 11))) return false

    return true
  }, [])

  const maskCPF = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})/, "$1-$2")
        .replace(/(-\d{2})\d+?$/, "$1"),
    [],
  )

  const maskPhone = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{2})(\d)/, "($1) $2")
        .replace(/(\d{5})(\d)/, "$1-$2")
        .replace(/(-\d{4})\d+?$/, "$1"),
    [],
  )

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const parseCurrency = (value: string): number => {
    return Number(value.replace(/[^\d,]/g, "").replace(",", "."))
  }

  const startLoading = useCallback((min: number, max: number, msg: string, onComplete: () => void) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg } })
    const duration = Math.random() * (max - min) + min
    setTimeout(() => {
      onComplete()
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }, duration)
  }, [])

  // --- Effects ---
  useEffect(() => {
    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        if (parsed.authToken) {
          window.location.href = "/dashboard"
          return
        }
        // We can't dispatch the entire state, so we'll handle this differently
        // For now, we'll skip the localStorage restoration to avoid complexity
      }
    } catch (e) {
      console.error("Failed to load state", e)
    }
  }, [])

  useEffect(() => {
    localStorage.setItem("locpay_app_state_v1", JSON.stringify(state))
  }, [state])

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }, [state.authStep])

  // --- Style Classes ---
  const commonInputClass =
    "h-12 bg-white border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 rounded-lg text-sm text-gray-900 placeholder:text-gray-400 transition-all duration-200 shadow-sm"
  const inputWithIconClass = "pl-10 pr-4"
  const mainButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const greenButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const redButtonClass =
    "w-full h-10 text-sm font-medium bg-red-50 border border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-all duration-200 rounded-lg"

  // --- Component Definitions ---

  // Authentication Form Components
  const CPFForm = () => {
    const cpfForm = useForm<z.infer<typeof cpfSchema>>({
      resolver: zodResolver(cpfSchema),
      defaultValues: { cpf: "" },
    })

    const onCPFSubmit = async (data: z.infer<typeof cpfSchema>) => {
      dispatch({ type: "SET_FORM_DATA", payload: { cpf: data.cpf } })
      dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Verificando CPF..." } })

      try {
        const response = await fetch("/api/auth/check-cpf", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ cpf: data.cpf }),
        })

        const result = await response.json()

        if (result.exists) {
          dispatch({ type: "SET_IS_NEW_USER", payload: false })
          dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
        } else {
          dispatch({ type: "SET_IS_NEW_USER", payload: true })
          dispatch({ type: "SET_AUTH_STEP", payload: "registration" })
        }
      } catch (error) {
        dispatch({ type: "SET_ERROR", payload: "Erro ao verificar CPF. Tente novamente." })
      } finally {
        dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
      }
    }

    return (
      <form onSubmit={cpfForm.handleSubmit(onCPFSubmit)} className="space-y-4">
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">Vamos começar!</h2>
          <p className="text-gray-600 text-sm">Digite seu CPF para continuar</p>
        </div>

        <Controller
          name="cpf"
          control={cpfForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={CreditCard}
              id="cpf"
              label="CPF"
              placeholder="000.000.000-00"
              value={field.value}
              onChange={(e) => {
                const maskedValue = maskCPF(e.target.value)
                field.onChange(maskedValue)
              }}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />

        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          {state.loading ? "Verificando..." : "CONTINUAR"}
          <ArrowRight className="w-4 h-4" />
        </Button>
      </form>
    )
  }

  const RegistrationForm = () => {
    const registrationForm = useForm<z.infer<typeof registrationSchema>>({
      resolver: zodResolver(registrationSchema),
      defaultValues: {
        nomeCompleto: "",
        whatsapp: "",
      },
    })

    const onRegistrationSubmit = async (data: z.infer<typeof registrationSchema>) => {
      dispatch({ type: "SET_FORM_DATA", payload: { nomeCompleto: data.nomeCompleto, whatsapp: data.whatsapp } })
      dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Enviando código WhatsApp..." } })

      try {
        const response = await fetch("/api/auth/send-whatsapp-code", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cpf: state.formData.cpf,
            nomeCompleto: data.nomeCompleto,
            whatsapp: data.whatsapp,
          }),
        })

        if (response.ok) {
          dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
          dispatch({ type: "SET_SUCCESS", payload: "Código enviado para seu WhatsApp!" })
        } else {
          dispatch({ type: "SET_ERROR", payload: "Erro ao enviar código. Tente novamente." })
        }
      } catch (error) {
        dispatch({ type: "SET_ERROR", payload: "Erro ao enviar código. Tente novamente." })
      } finally {
        dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
      }
    }

    return (
      <form onSubmit={registrationForm.handleSubmit(onRegistrationSubmit)} className="space-y-4">
        <div className="text-center mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">Quase lá!</h2>
          <p className="text-gray-600 text-sm">Complete seu cadastro</p>
        </div>

        <Controller
          name="nomeCompleto"
          control={registrationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Smile}
              id="nomeCompleto"
              label="Nome Completo"
              placeholder="Seu nome completo"
              value={field.value}
              onChange={field.onChange}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />

        <Controller
          name="whatsapp"
          control={registrationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Smartphone}
              id="whatsapp"
              label="WhatsApp"
              placeholder="(11) 99999-9999"
              value={field.value}
              onChange={(e) => {
                const maskedValue = maskPhone(e.target.value)
                field.onChange(maskedValue)
              }}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />

        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          {state.loading ? "Enviando..." : "ENVIAR CÓDIGO WHATSAPP"}
          <Smartphone className="w-4 h-4" />
        </Button>
      </form>
    )
  }



  const LoadingScreen = () => (
    <div className="fixed inset-0 bg-gradient-to-br from-[#0B4375]/95 to-[#0B4375]/95 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="text-center p-8 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-sm mx-4 border border-white/20">
        <div className="relative mb-8">
          <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin mx-auto">
            <div className="w-full h-full border-4 border-transparent border-t-blue-600 border-r-blue-700 rounded-full animate-spin"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg">
              <Check className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <p className="text-[#0B4375] font-bold text-lg mb-2" aria-live="polite">
            {state.loadingMsg}
          </p>
        </div>
      </div>
    </div>
  )



  const InputField = ({
    icon: Icon,
    id,
    placeholder,
    value,
    onChange,
    maxLength,
    type = "text",
    inputRef,
    label,
    error,
    hasError = false,
  }: {
    icon?: React.ComponentType<any>
    id: string
    placeholder: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    maxLength?: number
    type?: string
    inputRef?: React.RefObject<HTMLInputElement>
    label?: string
    error?: string
    hasError?: boolean
  }) => (
    <div className="space-y-2">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <div className="relative">
        {Icon && <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />}
        <Input
          ref={inputRef}
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          maxLength={maxLength}
          aria-invalid={hasError}
          className={`${commonInputClass} ${Icon ? inputWithIconClass : "px-3"} ${hasError ? "border-red-500 focus:border-red-500" : ""
            }`}
        />
      </div>
      {error && <p className="text-sm text-red-600 mt-1">{error}</p>}
    </div>
  )





  // --- Authentication Functions ---
  const handleCPFSubmit = async (cpf: string) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Verificando CPF..." } })

    try {
      const response = await fetch("/api/v1/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()

      if (response.ok && result.codeSent) {
        // User exists, proceed to verification
        dispatch({ type: "SET_IS_NEW_USER", payload: false })
        dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
        dispatch({ type: "SET_FORM_DATA", payload: { cpf } })
        dispatch({ type: "SET_SUCCESS", payload: "Código enviado para seu WhatsApp!" })
      } else if (response.status === 400 && result.message?.includes("Telefone e Nome é obrigatório")) {
        // New user, show registration fields
        dispatch({ type: "SET_IS_NEW_USER", payload: true })
        dispatch({ type: "SET_AUTH_STEP", payload: "registration" })
        dispatch({ type: "SET_FORM_DATA", payload: { cpf } })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao verificar CPF" })
      }
    } catch (error) {
      console.error("Erro ao verificar CPF:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleRegistrationSubmit = async (data: { nomeCompleto: string; cpf: string; whatsapp: string }) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Criando conta..." } })

    try {
      const response = await fetch("/api/v1/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.nomeCompleto,
          cpf: data.cpf.replace(/\D/g, ""),
          phone: data.whatsapp.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()

      if (response.ok && result.codeSent) {
        dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
        dispatch({ type: "SET_FORM_DATA", payload: data })
        dispatch({ type: "SET_SUCCESS", payload: "Conta criada com sucesso! Código enviado para seu WhatsApp." })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao criar conta" })
      }
    } catch (error) {
      console.error("Erro ao criar conta:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleVerificationSubmit = async (code: string) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Verificando código..." } })

    try {
      const response = await fetch("/api/v1/auth/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: state.formData.cpf.replace(/\D/g, ""),
          code: code,
        }),
      })

      const result = await response.json()

      if (response.ok && result.accessToken) {
        dispatch({ type: "SET_AUTH_TOKEN", payload: result.accessToken })
        dispatch({ type: "SET_USER", payload: result.user })
        dispatch({ type: "SET_AUTH_STEP", payload: "complete" })
        dispatch({ type: "SET_SUCCESS", payload: "Código verificado com sucesso! Redirecionando..." })

        const authData = {
          authToken: result.accessToken,
          user: result.user,
        }

        localStorage.setItem("x-auth-state", JSON.stringify(authData))

        setTimeout(() => {
          window.location.href = "/dashboard"
        }, 1500)
      } else {
        dispatch({
          type: "SET_ERROR",
          payload: result.message || "Código inválido. Verifique o código enviado pelo WhatsApp."
        })
      }
    } catch (error) {
      console.error("Erro ao verificar código:", error)
      dispatch({
        type: "SET_ERROR",
        payload: "Erro de conexão. Tente novamente."
      })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleResendCode = async () => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Reenviando código..." } })

    try {
      const response = await fetch("/api/v1/auth/resend-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: state.formData.cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()
      if (response.ok && result.codeSent) {
        dispatch({ type: "SET_SUCCESS", payload: "Código reenviado com sucesso! Verifique seu WhatsApp." })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao reenviar código" })
      }
    } catch (error) {
      console.error("Erro ao reenviar código:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }



  const VerificationForm = () => {
    const verificationForm = useForm<z.infer<typeof verificationSchema>>({
      resolver: zodResolver(verificationSchema),
      defaultValues: { code: "" },
    })

    const onVerificationSubmit = (data: z.infer<typeof verificationSchema>) => {
      handleVerificationSubmit(data.code)
    }

    return (
      <form onSubmit={verificationForm.handleSubmit(onVerificationSubmit)} className="space-y-4">
        {state.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {state.error}
          </div>
        )}
        {state.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
            {state.success}
          </div>
        )}
        <div className="text-center mb-4">
          <Smartphone className="w-12 h-12 text-blue-600 mx-auto mb-2" />
          <p className="text-sm font-medium text-gray-700">Código enviado via WhatsApp</p>
          <p className="text-xs text-gray-500">
            Verifique seu WhatsApp e digite o código de 6 dígitos
          </p>
        </div>
        <Controller
          name="code"
          control={verificationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Lock}
              id="code"
              placeholder="000000"
              value={field.value}
              onChange={(e) => field.onChange(e.target.value.replace(/\D/g, ""))}
              maxLength={6}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          <CheckCircle className="w-4 h-4" />
          {state.loading ? "Verificando..." : "Verificar Código"}
        </Button>
        <div className="flex justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={() => dispatch({ type: "SET_AUTH_STEP", payload: state.isNewUser ? "registration" : "cpf" })}
            className="text-sm text-gray-600"
          >
            ← Voltar
          </Button>
          <Button
            type="button"
            variant="ghost"
            onClick={handleResendCode}
            className="text-sm text-blue-600"
            disabled={state.loading}
          >
            Reenviar código
          </Button>
        </div>
      </form>
    )
  }

  const AuthCompleteForm = () => {
    setTimeout(() => {
      window.location.href = "/dashboard"
    }, 1500);

    return (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-800">Bem-vindo!</h3>
          <p className="text-sm text-gray-600">
            {state.isNewUser ? "Conta criada com sucesso!" : "Login realizado com sucesso!"}
          </p>
        </div>
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-700">
            Redirecionando para a próxima etapa...
          </p>
        </div>
      </div>
    )
  }





  // --- Main Render Function ---
  const renderAuthPage = () => {
    return (
      <section aria-label="Página inicial - Autenticação">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] relative overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
            <div className="absolute bottom-40 right-16 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          </div>
          <div className="relative z-10 px-4 py-8 flex flex-col min-h-screen">
            {/* Header */}
            <div className="flex justify-center mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 shadow-lg">
                <Image src="/images/locpay-logo.png" alt="LocPay" width={140} height={37} className="h-9 w-auto" />
              </div>
            </div>

            <div className="flex-1 flex flex-col justify-center text-center">
              {/* Hero Section */}
              <div className="mb-8">
                <div className="text-4xl mb-4">🚀</div>
                <h1 className="text-3xl font-bold text-white mb-4 leading-tight">
                  Realize seus Projetos com a Antecipação de Aluguel!
                </h1>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 mb-6 max-w-sm mx-auto">
                  <div className="flex items-center justify-center gap-2 text-white/90 text-sm">
                    <span>💰</span>
                    <span className="font-medium">
                      Preencha os dados e Antecipe até 12 meses de aluguel sem burocracia.
                    </span>
                  </div>
                </div>
                <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                  <Clock className="w-4 h-4 text-white" />
                  <span className="text-white text-sm font-medium">Processo 100% digital</span>
                  <Zap className="w-4 h-4 text-yellow-300" />
                </div>
              </div>

              {/* Testimonial Pill */}
              <div className="mb-6">
                <div className="bg-cyan-50/80 backdrop-blur-sm rounded-full px-4 py-2 border border-cyan-200/50 shadow-lg max-w-sm mx-auto">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🚀</span>
                    </div>
                    <span className="text-sm font-semibold text-cyan-700">
                      Mais de 1.000 proprietários já anteciparam com a LocPay
                    </span>
                  </div>
                </div>
              </div>

              {/* Authentication Form */}
              <div className="mb-8">
                <Card className="bg-white/95 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20 max-w-sm mx-auto">
                  <CardContent className="p-6">
                    {state.authStep === "cpf" && <CPFForm />}
                    {state.authStep === "registration" && <RegistrationForm />}
                    {state.authStep === "verification" && <VerificationForm />}
                    {state.authStep === "complete" && <AuthCompleteForm />}
                  </CardContent>
                </Card>
              </div>

              {/* Stats Pills */}
              <div className="flex flex-wrap justify-center gap-3 mb-8 px-4">
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-gray-700">+R$ 7M antecipados</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                      <Clock className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-gray-700">Aprovação em 1 hora</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-bold">%</span>
                    </div>
                    <span className="text-sm font-semibold text-gray-700">Melhores taxas do mercado</span>
                  </div>
                </div>
              </div>

              {/* Rating */}
              <div className="mb-6">
                <div className="flex justify-center gap-1 mb-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-300 fill-current" />
                  ))}
                </div>
                <p className="text-white/90 text-sm font-medium">Mais de 10.000 clientes satisfeitos</p>
              </div>
            </div>

            {/* Footer */}
            <div className="space-y-4">
              <div className="text-center">
                <Button
                  onClick={() =>
                    window.open(
                      "https://wa.me/5541999999999?text=Olá! Gostaria de saber mais sobre a antecipação de aluguel.",
                      "_blank",
                    )
                  }
                  variant="ghost"
                  className="text-white/80 hover:text-white hover:bg-white/10 text-sm border border-white/20 rounded-lg px-6 py-2 transition-all duration-200"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Falar com especialista
                </Button>
              </div>
              <div className="flex items-center justify-center gap-4 text-white/60 text-xs pt-4">
                <div className="flex items-center gap-1">
                  <ShieldCheck className="w-3 h-3" />
                  <span>SSL Seguro</span>
                </div>
                <div className="flex items-center gap-1">
                  <Verified className="w-3 h-3" />
                  <span>LGPD</span>
                </div>
                <div className="flex items-center gap-1">
                  <BadgeCheck className="w-3 h-3" />
                  <span>Certificado</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <main>
      <div className="relative">
        {state.loading && <LoadingScreen />}
        {renderAuthPage()}
      </div>
    </main>
  )
}
